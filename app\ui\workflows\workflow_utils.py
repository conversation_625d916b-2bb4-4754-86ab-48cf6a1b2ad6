import streamlit as st
import httpx
import asyncio
import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 检查认证是否启用
AUTH_ENABLED = os.getenv("AUTH_ENABLED", "true").lower() == "true"

# 工作流参数编辑器相关工具函数

def get_api_base_url():
    """获取API基础URL"""
    return f"http://localhost:{os.getenv('API_PORT', '18001')}"

def get_auth_headers() -> Dict[str, str]:
    """获取认证头"""
    headers = {}
    # 只有在认证启用且有token时才添加认证头
    if AUTH_ENABLED and "token" in st.session_state:
        headers["Authorization"] = f"Bearer {st.session_state.token}"
    return headers

async def api_request(method: str, endpoint: str, headers: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
    """发送 API 请求"""
    url = f"{get_api_base_url().rstrip('/')}/{endpoint.lstrip('/')}"
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.request(method, url, headers=headers, **kwargs)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 401 and AUTH_ENABLED:
                # 认证失败，清除登录状态
                if "logged_in" in st.session_state:
                    del st.session_state.logged_in
                    del st.session_state.token
                    del st.session_state.user_info
                st.error("登录已过期，请重新登录")
                st.stop()
            else:
                st.error(f"API 请求失败: {e.response.status_code} - {e.response.text}")
                return {"success": False, "error": e.response.text}
        except Exception as e:
            st.error(f"网络错误: {str(e)}")
            return {"success": False, "error": str(e)}

def extract_notes_from_workflow(workflow_json: Dict[str, Any]) -> str:
    """从工作流JSON中提取Note或Markdown Note节点的内容（兼容两种结构）"""
    notes: List[str] = []

    # 情况一：部分导出格式包含 nodes 列表
    if isinstance(workflow_json, dict) and isinstance(workflow_json.get("nodes"), list):
        nodes = workflow_json.get("nodes", [])
        for node in nodes:
            if not isinstance(node, dict):
                continue
            node_type = str(node.get("type", ""))

            if "note" in node_type.lower() or "markdown" in node_type.lower():
                widgets_values = node.get("widgets_values", [])
                if widgets_values:
                    text_content = widgets_values[0]
                    if isinstance(text_content, str) and text_content.strip():
                        notes.append(text_content.replace("#", "").strip())

                inputs = node.get("inputs", {}) or {}
                for field in ["markdown", "text", "content", "note", "description"]:
                    text_content = inputs.get(field)
                    if isinstance(text_content, str) and text_content.strip():
                        notes.append(text_content.strip())
                        break

    # 情况二：标准 ComfyUI 工作流为 {node_id: {class_type, inputs, ...}} 的映射
    elif isinstance(workflow_json, dict):
        for _, node in workflow_json.items():
            if not isinstance(node, dict):
                continue
            class_type = str(node.get("class_type", ""))
            title = str(node.get("_meta", {}).get("title", ""))
            is_note_like = (
                any(k in class_type.lower() for k in ["note", "markdown"]) or
                any(k in title.lower() for k in ["note", "markdown", "简介", "description"])
            )
            if not is_note_like:
                continue

            inputs = node.get("inputs", {}) or {}
            for field in ["markdown", "text", "content", "note", "description"]:
                val = inputs.get(field)
                if isinstance(val, str) and val.strip():
                    notes.append(val.strip())
                    break

            # 兜底 widgets_values
            wv = node.get("widgets_values")
            if isinstance(wv, list) and wv and isinstance(wv[0], str) and wv[0].strip():
                notes.append(wv[0].replace("#", "").strip())

    return "\n\n".join(dict.fromkeys(notes)) if notes else "暂无简介信息"

def classify_workflow_type(workflow_json: Dict[str, Any]) -> str:
    """根据工作流内容分类工作流类型（兼容两种结构）"""
    has_image_input = False
    has_text_input = False
    has_video_output = False
    has_image_output = False

    def inspect_type(t: str):
        nonlocal has_image_input, has_text_input, has_video_output, has_image_output
        tl = t.lower()
        if any(k in tl for k in ["loadimage", "imageload", "load image", "image loader", "loadimage"]):
            has_image_input = True
        if any(k in tl for k in ["cliptext", "textinput", "textencode", "text encode", "prompt"]):
            has_text_input = True
        if any(k in tl for k in ["videosave", "videoout", "animatediff", "save video", "video writer", "video output"]):
            has_video_output = True
        if any(k in tl for k in ["imagesave", "imageout", "saveimage", "save image", "image output"]):
            has_image_output = True

    # 结构一：nodes 列表
    if isinstance(workflow_json, dict) and isinstance(workflow_json.get("nodes"), list):
        for node in workflow_json.get("nodes", []):
            if isinstance(node, dict):
                inspect_type(str(node.get("type", "")))
    # 结构二：映射
    elif isinstance(workflow_json, dict):
        for _, node in workflow_json.items():
            if isinstance(node, dict):
                inspect_type(str(node.get("class_type", "")))

    if has_image_input and has_image_output:
        return "图生图"
    if has_image_input and has_video_output:
        return "图生视频"
    if has_text_input and has_video_output:
        return "文生视频"
    if has_text_input and has_image_output:
        return "文生图"
    return "其他"

def analyze_workflow_nodes(workflow_json: Dict[str, Any]) -> Dict[str, List[Dict]]:
    """分析工作流节点，提取可编辑的参数（兼容两种结构）"""
    editable_nodes: List[Dict] = []
    model_nodes: List[Dict] = []

    # 结构一：nodes 列表（较少见）
    if isinstance(workflow_json, dict) and isinstance(workflow_json.get("nodes"), list):
        for node in workflow_json.get("nodes", []):
            if not isinstance(node, dict):
                continue
            node_id = str(node.get("id", ""))
            node_type = str(node.get("type", ""))
            node_title = node.get("title", node_type)
            inputs = node.get("inputs", [])
            widgets_values = node.get("widgets_values", [])

            if is_model_loader_node(node_type):
                model_nodes.append({
                    "id": node_id,
                    "title": node_title,
                    "type": node_type,
                    "class_type": node_type,
                    "inputs": inputs,
                    "widgets_values": widgets_values,
                })
            elif is_editable_node(node_type):
                editable_params = extract_editable_params_from_comfyui_node(node)
                if editable_params:
                    editable_nodes.append({
                        "id": node_id,
                        "title": node_title,
                        "type": node_type,
                        "class_type": node_type,
                        "inputs": inputs,
                        "widgets_values": widgets_values,
                        "editable_params": editable_params,
                    })

    # 结构二：映射（ComfyUI 默认导出）
    elif isinstance(workflow_json, dict):
        for node_id, node_data in workflow_json.items():
            if not isinstance(node_data, dict):
                continue
            class_type = str(node_data.get("class_type", ""))
            node_title = node_data.get("_meta", {}).get("title", class_type)
            inputs = node_data.get("inputs", {}) or {}

            if is_model_loader_node(class_type):
                model_nodes.append({
                    "id": str(node_id),
                    "title": node_title,
                    "type": class_type,
                    "class_type": class_type,
                    "inputs": inputs,
                })
            elif is_editable_node(class_type):
                editable_params = extract_editable_params_from_node(str(node_id), class_type, node_title, inputs)
                if editable_params:
                    editable_nodes.append({
                        "id": str(node_id),
                        "title": node_title,
                        "type": class_type,
                        "class_type": class_type,
                        "inputs": inputs,
                        "editable_params": editable_params,
                    })

    return {"editable_nodes": editable_nodes, "model_nodes": model_nodes}

def is_model_loader_node(node_type: str) -> bool:
    """判断是否是模型加载节点"""
    model_keywords = [
        "checkpointloader", "loader", "load", "model",
        "vae", "controlnet", "lora", "embedding", "unet"
    ]
    node_type_lower = node_type.lower()
    return any(keyword in node_type_lower for keyword in model_keywords)

def is_editable_node(node_type: str) -> bool:
    """判断是否是可编辑节点（排除模型加载和纯说明类节点）"""
    # 排除的节点类型
    excluded_keywords = [
        "loader", "load", "model", "vae", "controlnet", "lora",
        "note", "comment", "group", "reroute", "preview", "save", "image"
    ]
    
    node_type_lower = node_type.lower()
    
    # 如果包含排除关键词，则不可编辑
    if any(keyword in node_type_lower for keyword in excluded_keywords):
        return False
    
    # 包含的节点类型（可编辑）
    included_keywords = [
        "sampler", "text", "clip", "encode", "condition",
        "prompt", "seed", "steps", "cfg", "denoise",
        "segment", "mask", "resize", "crop", "guidance",
        "ksampler", "cliptext"
    ]
    
    return any(keyword in node_type_lower for keyword in included_keywords)

def extract_editable_params_from_comfyui_node(node: Dict[str, Any]) -> List[Dict]:
    """从ComfyUI节点中提取可编辑参数"""
    editable_params = []

    node_id = str(node.get("id", ""))
    node_type = node.get("type", "")
    widgets_values = node.get("widgets_values", [])
    inputs = node.get("inputs", [])

    # 🚨 CRITICAL FIX: 检查节点是否应该被编辑
    if not is_editable_node(node_type):
        # 模型加载器和其他不可编辑节点应该被完全排除
        return editable_params
    
    # 处理widgets_values - 这是ComfyUI存储可编辑参数值的主要位置
    if widgets_values:
        # 根据节点类型推断参数名称和类型
        param_names = get_widget_param_names(node_type)
        
        for i, value in enumerate(widgets_values):
            if i < len(param_names):
                param_name = param_names[i]
                param_info = get_comfyui_param_info(param_name, value, node_type, i)
                if param_info:
                    param_info.update({
                        "node_id": node_id,
                        "path": f"{node_id}.widgets_values.{i}",
                        "param_name": param_name,
                        "current_value": value,
                        "widget_index": i
                    })
                    editable_params.append(param_info)
    
    # 处理inputs中的widget类型输入
    for input_data in inputs:
        if isinstance(input_data, dict):
            input_name = input_data.get("name", "")
            widget_info = input_data.get("widget", {})
            
            if widget_info and not input_data.get("link"):  # 没有连接的widget输入
                widget_name = widget_info.get("name", input_name)
                # 这些通常在widgets_values中，但我们可以记录其存在
                pass
    
    return editable_params

def get_widget_param_names(node_type: str) -> List[str]:
    """根据节点类型获取widget参数名称列表"""
    # 常见ComfyUI节点类型的参数映射
    widget_mappings = {
        "KSampler": ["seed", "steps", "cfg", "sampler_name", "scheduler", "denoise"],
        "CLIPTextEncode": ["text"],
        "FluxGuidance": ["guidance"],
        "LoraLoaderModelOnly": ["lora_name", "strength_model"],
        "DualCLIPLoader": ["clip_name1", "clip_name2", "type", "device"],
        "VAELoader": ["vae_name"],
        "UNETLoader": ["unet_name", "weight_dtype"],
        "LoadImage": ["image", "upload"],
        "SaveImage": ["filename_prefix"],
        "EmptyLatentImage": ["width", "height", "batch_size"],
        "UpscaleModelLoader": ["model_name"],
        "ConditioningZeroOut": [],
        "FluxKontextImageScale": [],
        "VAEEncode": [],
        "VAEDecode": [],
        "ImageUpscaleWithModel": [],
        "CR Latent Input Switch": ["Input"],
        "CR Latent Batch Size": ["batch_size"],
        "MarkdownNote": ["text"]
    }
    
    return widget_mappings.get(node_type, [f"param_{i}" for i in range(10)])  # 默认通用参数名

def get_comfyui_param_info(param_name: str, value: Any, node_type: str, widget_index: int) -> Optional[Dict]:
    """获取ComfyUI参数信息"""
    param_name_lower = param_name.lower()
    
    # 文本类型参数
    if isinstance(value, str):
        if any(keyword in param_name_lower for keyword in ["text", "prompt", "prefix", "name"]):
            return {
                "name": param_name,
                "type": "text",
                "widget_type": "text_area" if "text" in param_name_lower and len(str(value)) > 50 else "text_input",
                "description": f"{node_type} - {param_name}"
            }
        elif any(keyword in param_name_lower for keyword in ["sampler", "scheduler", "method", "device", "type"]):
            return {
                "name": param_name,
                "type": "select",
                "widget_type": "selectbox",
                "description": f"{node_type} - {param_name}",
                "options": get_comfyui_options(param_name_lower, value, node_type)
            }
    
    # 数值类型参数
    elif isinstance(value, (int, float)):
        param_config = get_comfyui_numeric_config(param_name_lower, value)
        if param_config:
            param_config.update({
                "name": param_name,
                "description": f"{node_type} - {param_name}"
            })
            return param_config
    
    # 布尔类型参数
    elif isinstance(value, bool):
        return {
            "name": param_name,
            "type": "bool",
            "widget_type": "checkbox",
            "description": f"{node_type} - {param_name}"
        }
    
    return None

def get_comfyui_options(param_name: str, current_value: str, node_type: str) -> List[str]:
    """获取ComfyUI参数的选项列表"""
    options_map = {
        "sampler_name": ["euler", "euler_a", "heun", "dpm_2", "dpm_2_a", "lms", "dpm_fast", "dpm_adaptive", "dpmpp_2s_a", "dpmpp_2m", "dpmpp_sde", "ddim", "uni_pc"],
        "scheduler": ["normal", "karras", "exponential", "sgm_uniform", "simple", "ddim_uniform"],
        "device": ["auto", "cpu", "cuda", "mps"],
        "type": ["sdxl", "sd3", "flux", "stable_cascade"],
        "weight_dtype": ["default", "fp8_e4m3fn", "fp8_e5m2", "fp16", "fp32"]
    }
    
    base_options = options_map.get(param_name, [])
    
    # 确保当前值在选项中
    if current_value and current_value not in base_options:
        base_options.insert(0, current_value)
    
    return base_options if base_options else [current_value] if current_value else ["default"]

def get_comfyui_numeric_config(param_name: str, value: float) -> Optional[Dict]:
    """获取ComfyUI数值参数配置"""
    configs = {
        "seed": {"type": "int", "widget_type": "number_input", "min_value": 0, "max_value": 2**32-1, "step": 1},
        "steps": {"type": "int", "widget_type": "slider", "min_value": 1, "max_value": 100, "step": 1},
        "cfg": {"type": "float", "widget_type": "slider", "min_value": 0.0, "max_value": 30.0, "step": 0.1},
        "denoise": {"type": "float", "widget_type": "slider", "min_value": 0.0, "max_value": 1.0, "step": 0.01},
        "guidance": {"type": "float", "widget_type": "slider", "min_value": 0.0, "max_value": 10.0, "step": 0.1},
        "strength_model": {"type": "float", "widget_type": "slider", "min_value": 0.0, "max_value": 2.0, "step": 0.01},
        "width": {"type": "int", "widget_type": "number_input", "min_value": 64, "max_value": 4096, "step": 8},
        "height": {"type": "int", "widget_type": "number_input", "min_value": 64, "max_value": 4096, "step": 8},
        "batch_size": {"type": "int", "widget_type": "slider", "min_value": 1, "max_value": 16, "step": 1},
        "input": {"type": "int", "widget_type": "slider", "min_value": 1, "max_value": 10, "step": 1}
    }
    
    return configs.get(param_name)

def extract_editable_params_from_node(node_id: str, class_type: str, node_title: str, inputs: Dict) -> List[Dict]:
    """从节点中提取可编辑参数"""
    editable_params = []

    # 🚨 CRITICAL FIX: 检查节点是否应该被编辑
    if not is_editable_node(class_type):
        # 模型加载器和其他不可编辑节点应该被完全排除
        return editable_params
    
    for input_name, input_value in inputs.items():
        param_info = get_param_info(input_name, input_value, class_type)
        if param_info:
            param_info.update({
                "node_id": node_id,
                "path": f"{node_id}.inputs.{input_name}",
                "current_value": input_value
            })
            editable_params.append(param_info)
    
    return editable_params

def get_param_info(input_name: str, input_value: Any, class_type: str) -> Optional[Dict]:
    """获取参数信息，包括类型、控件类型等"""
    input_name_lower = input_name.lower()
    
    # 排除连接类型的输入（通常是列表格式，如 ["node_id", output_index]）
    if isinstance(input_value, list):
        return None
    
    # 文本类型参数
    if isinstance(input_value, str):
        if any(keyword in input_name_lower for keyword in ["text", "prompt", "description", "content"]):
            return {
                "name": input_name,
                "type": "text",
                "widget_type": "text_area" if len(str(input_value)) > 50 else "text_input",
                "description": f"{class_type} - {input_name}"
            }
        elif any(keyword in input_name_lower for keyword in ["sampler", "scheduler", "method"]):
            return {
                "name": input_name,
                "type": "select",
                "widget_type": "selectbox",
                "description": f"{class_type} - {input_name}",
                "options": get_common_options(input_name_lower, input_value)
            }
    
    # 数值类型参数
    elif isinstance(input_value, (int, float)):
        param_config = get_numeric_param_config(input_name_lower, input_value)
        if param_config:
            param_config.update({
                "name": input_name,
                "description": f"{class_type} - {input_name}"
            })
            return param_config
    
    # 布尔类型参数
    elif isinstance(input_value, bool):
        return {
            "name": input_name,
            "type": "bool",
            "widget_type": "checkbox",
            "description": f"{class_type} - {input_name}"
        }
    
    return None

def get_common_options(param_name: str, current_value: str) -> List[str]:
    """获取常见参数的选项列表"""
    options_map = {
        "sampler": ["euler", "euler_a", "heun", "dpm_2", "dpm_2_a", "lms", "dpm_fast", "dpm_adaptive", "dpmpp_2s_a", "dpmpp_2m", "dpmpp_sde", "ddim"],
        "scheduler": ["normal", "karras", "exponential", "sgm_uniform", "simple", "ddim_uniform"],
        "method": ["lanczos", "nearest", "linear", "cubic", "area"]
    }
    
    for key, options in options_map.items():
        if key in param_name:
            # 确保当前值在选项中
            if current_value not in options:
                options.append(current_value)
            return options
    
    return [current_value]

def get_numeric_param_config(param_name: str, current_value: float) -> Optional[Dict]:
    """获取数值参数配置"""
    # 常见数值参数的配置
    configs = {
        "steps": {"type": "int", "widget_type": "number_input", "min_value": 1, "max_value": 150, "step": 1},
        "cfg": {"type": "float", "widget_type": "number_input", "min_value": 0.0, "max_value": 30.0, "step": 0.1},
        "scale": {"type": "float", "widget_type": "slider", "min_value": 0.0, "max_value": 2.0, "step": 0.1},
        "denoise": {"type": "float", "widget_type": "slider", "min_value": 0.0, "max_value": 1.0, "step": 0.01},
        "strength": {"type": "float", "widget_type": "slider", "min_value": 0.0, "max_value": 1.0, "step": 0.01},
        "seed": {"type": "int", "widget_type": "number_input", "min_value": -1, "max_value": 2**32-1, "step": 1},
        "width": {"type": "int", "widget_type": "number_input", "min_value": 64, "max_value": 2048, "step": 8},
        "height": {"type": "int", "widget_type": "number_input", "min_value": 64, "max_value": 2048, "step": 8},
    }
    
    for key, config in configs.items():
        if key in param_name:
            return config
    
    # 默认配置
    if isinstance(current_value, int):
        return {"type": "int", "widget_type": "number_input", "min_value": 0, "max_value": 1000, "step": 1}
    else:
        return {"type": "float", "widget_type": "number_input", "min_value": 0.0, "max_value": 100.0, "step": 0.1}

def render_param_editor(param_info: Dict, value_key: str) -> Any:
    """渲染参数编辑器控件"""
    widget_type = param_info["widget_type"]
    param_name = param_info["name"]
    description = param_info["description"]
    current_value = param_info["current_value"]
    
    if widget_type == "text_input":
        return st.text_input(
            param_name,
            value=current_value,
            key=value_key,
            help=description
        )
    
    elif widget_type == "text_area":
        return st.text_area(
            param_name,
            value=current_value,
            key=value_key,
            help=description,
            height=100
        )
    
    elif widget_type == "number_input":
        if param_info["type"] == "int":
            return st.number_input(
                param_name,
                value=int(current_value),
                min_value=param_info.get("min_value", 0),
                max_value=param_info.get("max_value", 1000),
                step=param_info.get("step", 1),
                key=value_key,
                help=description
            )
        else:
            return st.number_input(
                param_name,
                value=float(current_value),
                min_value=param_info.get("min_value", 0.0),
                max_value=param_info.get("max_value", 100.0),
                step=param_info.get("step", 0.1),
                key=value_key,
                help=description
            )
    
    elif widget_type == "slider":
        if param_info["type"] == "int":
            return st.slider(
                param_name,
                min_value=param_info.get("min_value", 0),
                max_value=param_info.get("max_value", 100),
                value=int(current_value),
                step=param_info.get("step", 1),
                key=value_key,
                help=description
            )
        else:
            return st.slider(
                param_name,
                min_value=param_info.get("min_value", 0.0),
                max_value=param_info.get("max_value", 1.0),
                value=float(current_value),
                step=param_info.get("step", 0.01),
                key=value_key,
                help=description
            )
    
    elif widget_type == "selectbox":
        options = param_info.get("options", [current_value])
        current_index = 0
        if current_value in options:
            current_index = options.index(current_value)
        
        return st.selectbox(
            param_name,
            options=options,
            index=current_index,
            key=value_key,
            help=description
        )
    
    elif widget_type == "checkbox":
        return st.checkbox(
            param_name,
            value=bool(current_value),
            key=value_key,
            help=description
        )
    
    else:
        # 默认为文本输入
        return st.text_input(
            param_name,
            value=str(current_value),
            key=value_key,
            help=description
        )
