import streamlit as st
import time
import asyncio
from typing import Dict, Any, Optional
from ..workflows.workflow_utils import api_request, get_auth_headers


def show_real_time_progress_monitor(task_id: str, title: str = "任务进度监控"):
    """显示实时任务进度监控组件"""
    
    st.markdown(f"### 🔄 {title}")
    
    # 创建容器
    status_container = st.empty()
    progress_container = st.empty()
    details_container = st.empty()
    
    # 控制按钮
    col1, col2, col3 = st.columns([1, 1, 3])
    with col1:
        if st.button("⏹️ 停止监控", key=f"stop_monitor_{task_id}"):
            st.session_state[f"stop_monitor_{task_id}"] = True
            return
    
    with col2:
        auto_refresh = st.checkbox("🔄 自动刷新", value=True, key=f"auto_refresh_{task_id}")
    
    # 初始化停止标志
    if f"stop_monitor_{task_id}" not in st.session_state:
        st.session_state[f"stop_monitor_{task_id}"] = False
    
    # 监控循环
    max_checks = 240  # 最多检查20分钟
    check_count = 0
    
    while check_count < max_checks and not st.session_state.get(f"stop_monitor_{task_id}", False):
        try:
            # 获取任务状态
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            task_status = loop.run_until_complete(
                api_request("GET", f"/status/{task_id}", headers=get_auth_headers())
            )
            loop.close()
            
            if task_status and isinstance(task_status, dict):
                status = task_status.get("status", "unknown")
                workflow_progress = task_status.get("workflow_progress")
                batch_progress = task_status.get("batch_progress")
                
                # 显示整体状态
                status_color = {
                    "pending": "🟡",
                    "running": "🔵", 
                    "completed": "🟢",
                    "failed": "🔴"
                }.get(status, "⚪")
                
                status_container.markdown(f"**{status_color} 任务状态: {status.upper()}**")
                
                # 显示工作流进度
                if workflow_progress:
                    current_node = workflow_progress.get("current_node", "")
                    overall_progress = workflow_progress.get("overall_progress", 0.0)
                    
                    with progress_container.container():
                        st.markdown("**🔧 工作流执行进度**")
                        st.progress(overall_progress / 100.0)
                        
                        if current_node:
                            st.info(f"🔄 当前执行节点: {current_node}")
                        
                        # 显示节点详情
                        nodes = workflow_progress.get("nodes", [])
                        if nodes:
                            with st.expander("📋 节点执行详情", expanded=False):
                                for node in nodes:
                                    node_status = node.get("status", "pending")
                                    node_icon = {
                                        "pending": "⏳",
                                        "executing": "🔄",
                                        "completed": "✅",
                                        "failed": "❌"
                                    }.get(node_status, "⚪")
                                    
                                    st.markdown(f"{node_icon} **节点 {node.get('node_id')}**: {node_status}")
                
                # 显示批量进度
                if batch_progress:
                    current = batch_progress.get("current_index", 0)
                    total = batch_progress.get("total_count", 0)
                    current_file = batch_progress.get("current_filename", "")
                    completed = len(batch_progress.get("completed_files", []))
                    failed = len(batch_progress.get("failed_files", []))
                    
                    # 计算批量进度百分比
                    batch_percent = (completed / total * 100) if total > 0 else 0
                    
                    with details_container.container():
                        st.markdown("**📁 批量处理进度**")
                        st.progress(batch_percent / 100.0)
                        
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("进度", f"{current}/{total}")
                        with col2:
                            st.metric("当前文件", current_file if current_file else "准备中")
                        with col3:
                            st.metric("已完成", completed, delta=f"+{completed}")
                        with col4:
                            st.metric("失败", failed, delta=f"+{failed}" if failed > 0 else None)
                        
                        # 显示失败文件列表
                        failed_files = batch_progress.get("failed_files", [])
                        if failed_files:
                            with st.expander("❌ 失败文件列表", expanded=False):
                                for failed_file in failed_files:
                                    st.markdown(f"- {failed_file}")
                
                # 检查是否完成
                if status in ["completed", "failed"]:
                    if status == "completed":
                        st.success("🎉 任务执行完成！")
                        
                        # 显示结果摘要
                        result = task_status.get("result", {})
                        if result.get("batch_summary"):
                            summary = result["batch_summary"]
                            st.info(f"📊 批量处理摘要: 总计 {summary.get('total_processed', 0)} 张，"
                                   f"成功 {summary.get('successful', 0)} 张，"
                                   f"失败 {summary.get('failed', 0)} 张")
                    else:
                        error_msg = task_status.get("error_message", "未知错误")
                        st.error(f"❌ 任务执行失败: {error_msg}")
                    break
            
            # 如果不是自动刷新模式，只检查一次
            if not auto_refresh:
                break
            
            # 等待5秒后再次检查
            time.sleep(5)
            check_count += 1
            
        except Exception as e:
            status_container.error(f"❌ 获取进度失败: {str(e)}")
            break
    
    # 清理停止标志
    if f"stop_monitor_{task_id}" in st.session_state:
        del st.session_state[f"stop_monitor_{task_id}"]


def show_task_list_with_progress():
    """显示带进度的任务列表"""
    st.markdown("### 📋 任务列表")
    
    try:
        # 获取任务列表
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        tasks = loop.run_until_complete(
            api_request("GET", "/status", headers=get_auth_headers())
        )
        loop.close()
        
        if tasks and isinstance(tasks, list):
            for task in tasks:
                task_id = task.get("task_id", "")
                status = task.get("status", "unknown")
                workflow_key = task.get("workflow_key", "")
                created_at = task.get("created_at", "")
                
                # 状态图标
                status_icon = {
                    "pending": "🟡",
                    "running": "🔵", 
                    "completed": "🟢",
                    "failed": "🔴"
                }.get(status, "⚪")
                
                with st.expander(f"{status_icon} {workflow_key} - {task_id[:8]}", expanded=(status == "running")):
                    col1, col2 = st.columns([2, 1])
                    
                    with col1:
                        st.markdown(f"**任务ID**: {task_id}")
                        st.markdown(f"**工作流**: {workflow_key}")
                        st.markdown(f"**状态**: {status}")
                        st.markdown(f"**创建时间**: {created_at}")
                    
                    with col2:
                        if status == "running":
                            if st.button("📊 查看详细进度", key=f"detail_{task_id}"):
                                show_real_time_progress_monitor(task_id, f"任务 {task_id[:8]} 详细进度")
                    
                    # 显示简化的进度信息
                    batch_progress = task.get("batch_progress")
                    if batch_progress:
                        current = batch_progress.get("current_index", 0)
                        total = batch_progress.get("total_count", 0)
                        st.progress(current / total if total > 0 else 0)
                        st.caption(f"批量进度: {current}/{total}")
        else:
            st.info("暂无任务")
            
    except Exception as e:
        st.error(f"获取任务列表失败: {str(e)}")
