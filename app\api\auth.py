import os
import yaml
import json
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, List, Any
from fastapi import HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from .schemas import UserInfo, LoginRequest, LoginResponse

# JWT 配置（当作依赖使用时允许缺少 Authorization 头，以便在关闭认证时正常放行）
security = HTTPBearer(auto_error=False)

class AuthManager:
    def __init__(self):
        self.auth_enabled = os.getenv("AUTH_ENABLED", "true").lower() == "true"
        self.auth_keys_file = os.getenv("AUTH_KEYS_FILE", "app/config/auth_keys.yaml")
        self.auth_secret = os.getenv("AUTH_SECRET", "your-secret-key-change-this")
        self.session_ttl_min = int(os.getenv("AUTH_SESSION_TTL_MIN", "1440"))
        self.auth_mode = os.getenv("AUTH_MODE", "keys")
        self.auth_online_enforce = os.getenv("AUTH_ONLINE_ENFORCE", "false").lower() == "true"
        self.license_server_url = os.getenv("LICENSE_SERVER_URL", "")
        
        # 白名单相关配置
        self.whitelist_mode = os.getenv("WHITELIST_MODE", "")
        self.whitelist_url = os.getenv("WHITELIST_URL", "")
        self.whitelist_file = os.getenv("WHITELIST_FILE", "app/config/whitelist.json")
        self.whitelist_pubkey_file = os.getenv("WHITELIST_PUBKEY_FILE", "app/config/whitelist_pubkey.pem")
        self.whitelist_ttl_min = int(os.getenv("WHITELIST_TTL_MIN", "60"))
        
        # 内存缓存
        self._keys_cache: Optional[Dict] = None
        self._whitelist_cache: Optional[Dict] = None
        self._last_keys_load: Optional[datetime] = None
        self._last_whitelist_load: Optional[datetime] = None

    def load_keys(self) -> Dict:
        """加载访问密钥配置"""
        if not self.auth_enabled:
            return {}
            
        keys_path = Path(self.auth_keys_file)
        if not keys_path.exists():
            # 如果没有真实的密钥文件，返回空配置
            return {}
            
        try:
            with open(keys_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                self._keys_cache = data
                self._last_keys_load = datetime.now(timezone.utc)
                return data
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"无法加载认证密钥配置: {str(e)}"
            )

    def load_whitelist(self) -> Dict:
        """加载白名单配置 - 占位实现"""
        if not self.whitelist_mode:
            return {"entries": []}
            
        if self.whitelist_mode == "online":
            # 在线模式 - 暂时返回 501
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="在线白名单模式尚未实现"
            )
        elif self.whitelist_mode == "file":
            # 文件模式
            whitelist_path = Path(self.whitelist_file)
            if not whitelist_path.exists():
                return {"entries": []}
                
            try:
                with open(whitelist_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # TODO: 验证签名
                    self._whitelist_cache = data
                    self._last_whitelist_load = datetime.now(timezone.utc)
                    return data
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"无法加载白名单配置: {str(e)}"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的白名单模式: {self.whitelist_mode}"
            )

    def verify_access_key(self, username: str, access_key: str) -> Optional[UserInfo]:
        """验证用户名和访问密钥"""
        if not self.auth_enabled:
            # 认证已禁用，返回默认管理员权限
            return UserInfo(username=username, roles=["admin"])
            
        keys_data = self.load_keys()
        users = keys_data.get("users", {})
        
        if username not in users:
            return None
            
        user_data = users[username]
        if user_data.get("access_key") != access_key:
            return None
            
        # 检查过期时间
        expires_str = user_data.get("expires_utc")
        if expires_str:
            try:
                expires_dt = datetime.fromisoformat(expires_str.replace('Z', '+00:00'))
                if datetime.now(timezone.utc) > expires_dt:
                    return None
            except ValueError:
                return None
                
        roles = user_data.get("roles", ["user"])
        return UserInfo(username=username, roles=roles)

    def create_token(self, user_info: UserInfo) -> str:
        """创建 JWT 令牌"""
        now = datetime.now(timezone.utc)
        expires_at = now + timedelta(minutes=self.session_ttl_min)
        
        payload = {
            "sub": user_info.username,
            "roles": user_info.roles,
            "iat": now,
            "exp": expires_at
        }
        
        token = jwt.encode(payload, self.auth_secret, algorithm="HS256")
        return token

    def verify_token(self, token: str) -> Optional[UserInfo]:
        """验证 JWT 令牌"""
        if not self.auth_enabled:
            return UserInfo(username="anonymous", roles=["admin"])
        if not token:
            return None
        try:
            payload = jwt.decode(token, self.auth_secret, algorithms=["HS256"])
            username = payload.get("sub")
            roles = payload.get("roles", ["user"])
            
            if not username:
                return None
                
            return UserInfo(username=username, roles=roles)
        except JWTError:
            return None

    # 占位方法 - 扫码登录相关
    def init_qr_login(self) -> Dict[str, Any]:
        """初始化扫码登录 - 占位实现"""
        if not self.license_server_url or self.auth_mode != "online":
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="扫码登录需要配置 LICENSE_SERVER_URL 且 AUTH_MODE=online（未实现/未配置）"
            )
        # TODO: 实现扫码登录初始化
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="扫码登录功能尚未实现"
        )

    def check_qr_status(self, login_id: str) -> Dict[str, Any]:
        """检查扫码登录状态 - 占位实现"""
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="扫码登录状态查询功能尚未实现"
        )

    def redeem_qr_code(self, login_id: str) -> LoginResponse:
        """兑换扫码登录结果 - 占位实现"""
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="扫码登录兑换功能尚未实现"
        )

# 全局认证管理器实例
auth_manager = AuthManager()

# 依赖注入函数
async def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> UserInfo:
    """获取当前用户信息"""
    # 动态读取环境变量，便于测试在用例内切换 AUTH_ENABLED
    auth_enabled = os.getenv("AUTH_ENABLED", "true").lower() == "true"
    
    if not auth_enabled:
        return UserInfo(username="anonymous", roles=["admin"])

    # 认证开启时但未提供凭据
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供访问令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials
    user = auth_manager.verify_token(token)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

async def get_admin_user(current_user: UserInfo = Depends(get_current_user)) -> UserInfo:
    """获取管理员用户"""
    if "admin" not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user

# 可选依赖 - 允许匿名访问
async def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))) -> Optional[UserInfo]:
    """可选的用户认证（允许匿名访问）"""
    if not credentials:
        return None
    return await get_current_user(credentials)
