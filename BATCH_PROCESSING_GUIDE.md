# 批量图片处理和实时进度监控功能使用指南

## 🎯 功能概述

本次更新为FreemanWorkHub添加了两个重要功能：

### 1. 批量图片处理功能
- 支持图生图类型工作流的批量处理
- 自动遍历输入目录中的所有图片文件
- 逐一提交给ComfyUI进行处理
- 自动从ComfyUI输出目录提取生成结果到用户指定目录
- 显示批量处理进度（第几张/共几张）

### 2. ComfyUI实时进度监控
- 通过WebSocket连接ComfyUI获取实时进度
- 显示当前执行的节点信息
- 显示整体执行进度百分比
- 支持任务状态实时更新

## 🚀 使用方法

### 批量图片处理

1. **准备输入图片**
   - 将需要处理的图片放在一个目录中
   - 支持的格式：PNG, JPG, JPEG, BMP, TIFF, WEBP, GIF

2. **配置工作流**
   - 进入"工作流管理"页面
   - 选择图生图类型的工作流
   - 点击"配置"按钮

3. **设置目录**
   - 在"输入图片目录"中设置包含待处理图片的目录
   - 在"输出目录"中设置生成图片的保存目录
   - 系统会自动显示输入目录中的图片数量

4. **启用批量模式**
   - 当输入目录包含多张图片时，会显示"批量处理模式"选项
   - 勾选该选项启用批量处理

5. **开始处理**
   - 点击"开始运行"按钮
   - 系统会显示实时进度监控界面
   - 显示当前处理的图片和整体进度

### 实时进度监控

1. **任务监控页面**
   - 点击侧边栏的"任务监控"进入监控页面
   - 查看所有任务的实时状态

2. **进度信息**
   - 🟡 等待中：任务已创建，等待执行
   - 🔵 运行中：任务正在执行，显示详细进度
   - 🟢 已完成：任务执行成功
   - 🔴 失败：任务执行失败，显示错误信息

3. **详细进度**
   - 对于运行中的任务，可以查看详细进度
   - 显示当前执行的ComfyUI节点
   - 显示整体执行进度百分比
   - 批量任务显示当前处理的图片信息

## 🔧 技术实现

### 后端改进

1. **扩展API接口**
   - 添加`batch_mode`参数到`RunWorkflowRequest`
   - 支持`input_dir`和`output_dir`参数

2. **批量处理服务**
   - 新增`BatchProcessingService`类
   - 实现图片文件遍历和批量提交逻辑
   - 自动提取ComfyUI输出文件到用户目录

3. **WebSocket进度监控**
   - 在`ComfyUIClient`中添加WebSocket连接功能
   - 实时监听ComfyUI的执行事件
   - 支持进度回调和状态更新

4. **进度数据结构**
   - 新增`BatchProgress`、`NodeProgress`、`WorkflowProgress`模型
   - 扩展`TaskInfo`模型支持详细进度信息

### 前端改进

1. **工作流配置页面**
   - 添加批量处理模式选项
   - 集成实时进度监控组件
   - 显示输入目录图片统计

2. **进度监控组件**
   - 创建可复用的进度显示组件
   - 支持批量处理和单任务进度显示
   - 实时更新任务状态

3. **任务监控页面**
   - 完整的任务列表和状态显示
   - 按状态分组显示任务
   - 支持详细进度查看

## 📋 配置要求

### 环境变量
```bash
# ComfyUI连接配置
COMFYUI_BASE_URL=http://localhost:8188
COMFYUI_INPUT_DIR=ComfyUI/input
COMFYUI_OUTPUT_DIR=ComfyUI/output
```

### 依赖包
```bash
pip install websockets>=11.0.3
```

## 🔍 使用注意事项

1. **ComfyUI服务**
   - 确保ComfyUI服务正在运行
   - 确保WebSocket端口可访问

2. **目录权限**
   - 确保输入目录可读
   - 确保输出目录可写
   - 确保ComfyUI的input/output目录可访问

3. **图片格式**
   - 支持常见图片格式
   - 建议使用PNG或JPG格式以获得最佳兼容性

4. **批量处理**
   - 大量图片处理可能需要较长时间
   - 建议分批处理，避免一次处理过多图片
   - 监控ComfyUI的资源使用情况

## 🐛 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查ComfyUI是否启用WebSocket
   - 检查防火墙设置
   - 确认端口8188可访问

2. **批量处理失败**
   - 检查输入目录是否包含有效图片
   - 检查ComfyUI input目录权限
   - 查看后端日志获取详细错误信息

3. **进度显示异常**
   - 刷新页面重新加载组件
   - 检查API服务是否正常运行
   - 查看浏览器控制台错误信息

### 调试方法

1. **查看后端日志**
   ```bash
   # 运行API服务时查看控制台输出
   python -m app.api.main
   ```

2. **测试连接**
   ```bash
   # 运行测试脚本
   python test_batch_processing.py
   ```

3. **检查ComfyUI状态**
   - 访问 http://localhost:8188 确认ComfyUI正常运行
   - 检查ComfyUI的队列状态

## 🎉 功能完成状态

✅ **已完成功能**
- 批量图片处理核心逻辑
- ComfyUI输出文件自动提取
- WebSocket实时进度监控
- 前端进度显示组件
- 任务监控页面集成

🔄 **后续优化建议**
- 添加批量处理的暂停/恢复功能
- 支持更多图片格式
- 优化大批量处理的性能
- 添加处理结果预览功能
- 支持批量处理的参数模板
