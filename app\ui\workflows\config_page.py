import streamlit as st
import asyncio
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
import tkinter as tk
from tkinter import filedialog
import threading
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 检查认证是否启用
AUTH_ENABLED = os.getenv("AUTH_ENABLED", "true").lower() == "true"

from .workflow_utils import (
    api_request, get_auth_headers, extract_notes_from_workflow,
    classify_workflow_type, analyze_workflow_nodes, render_param_editor
)

def select_folder_with_dialog():
    """使用文件对话框选择文件夹"""
    def _select_folder():
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        folder_path = filedialog.askdirectory(title="选择文件夹")
        root.destroy()
        return folder_path
    
    folder_path = _select_folder()
    return folder_path

def show_workflow_config_page():
    """显示工作流配置页面"""
    # 检查认证状态
    if AUTH_ENABLED and ("logged_in" not in st.session_state or not st.session_state.logged_in):
        st.warning("⚠️ 请先登录才能使用工作流配置功能")
        if st.button("🔙 返回主页"):
            st.session_state.page = "home"
            st.rerun()
        return
    
    # 检查是否选择了工作流
    if "selected_workflow" not in st.session_state:
        st.error("❌ 未选择工作流")
        if st.button("🔙 返回工作流列表"):
            st.session_state.page = "workflows"
            st.rerun()
        return
    
    workflow = st.session_state.selected_workflow
    workflow_json = workflow.get("workflow_json", {})
    
    # 页面标题
    st.title("🎯 工作流配置")
    
    # 返回按钮
    col1, col2 = st.columns([1, 5])
    with col1:
        if st.button("🔙 返回", use_container_width=True):
            st.session_state.page = "workflows"
            st.rerun()
    
    with col2:
        st.markdown(f"### {workflow.get('metadata', {}).get('name', '未知工作流')}")
    
    # 工作流基本信息
    with st.expander("📋 工作流基本信息", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**名称:**")
            st.text(workflow.get('metadata', {}).get('name', '未知'))
            
            st.markdown("**类型:**")
            workflow_type = classify_workflow_type(workflow_json)
            st.text(workflow_type)
            
        with col2:
            st.markdown("**标签:**")
            tags = workflow.get('metadata', {}).get('tags', [])
            st.text(", ".join(tags) if tags else "无标签")
            
            st.markdown("**文件路径:**")
            file_path = workflow.get('file_path', '')
            st.text(file_path if file_path else "未知路径")
    
    # 工作流简介
    with st.expander("📖 工作流简介", expanded=True):
        notes_content = extract_notes_from_workflow(workflow_json)
        if notes_content and notes_content != "暂无简介信息":
            st.markdown(notes_content)
        else:
            st.info("暂无简介信息，请在ComfyUI中为工作流添加Note或Markdown Note节点来提供说明")
    
    # 分析工作流节点
    nodes_analysis = analyze_workflow_nodes(workflow_json)
    editable_nodes = nodes_analysis["editable_nodes"]
    model_nodes = nodes_analysis["model_nodes"]
    
    # 关键参数设置
    st.markdown("---")
    st.subheader("⚙️ 关键参数设置")
    
    if editable_nodes:
        # 初始化参数值存储
        if "workflow_params" not in st.session_state:
            st.session_state.workflow_params = {}
        
        workflow_key = workflow.get('key', 'unknown')
        if workflow_key not in st.session_state.workflow_params:
            st.session_state.workflow_params[workflow_key] = {}
        
        # 为每个可编辑节点创建一个标签页式的卡片
        for i, node in enumerate(editable_nodes):
            node_id = node["id"]
            node_title = node["title"]
            class_type = node["class_type"]
            editable_params = node["editable_params"]
            
            # 检查是否是开发模式工作流
            is_dev_workflow = "DEV" in workflow.get('metadata', {}).get('tags', [])
            
            with st.expander(f"🔧 {node_title} ({class_type})", expanded=False):
                # 风险警告和启用开关
                enable_key = f"{workflow_key}_node_{node_id}_enable"
                
                if is_dev_workflow:
                    st.warning("⚠️ 这是开发中的工作流，某些参数可能不稳定，编辑时请谨慎")
                
                st.warning("⚠️ 编辑节点参数有风险，可能导致工作流运行失败，确认要编辑吗？")
                enable_edit = st.checkbox(
                    "确认启用编辑",
                    key=enable_key,
                    help="勾选后才能编辑此节点的参数"
                )
                
                if enable_edit:
                    st.success("✅ 编辑模式已启用")
                    
                    # 渲染参数编辑器
                    cols = st.columns(2) if len(editable_params) > 1 else [st.container()]
                    
                    for j, param_info in enumerate(editable_params):
                        param_path = param_info["path"]

                        # 添加时间戳到key中，确保重新载入时组件会重新创建
                        reload_timestamp = st.session_state.get(f"workflow_reload_timestamp_{workflow_key}", 0)
                        param_key = f"{workflow_key}_{param_path}_{reload_timestamp}"
                        
                        with cols[j % len(cols)]:
                            new_value = render_param_editor(param_info, param_key)
                            
                            # 存储参数值
                            st.session_state.workflow_params[workflow_key][param_path] = new_value
                            
                            # 显示参数路径（用于调试）
                            with st.expander("🔍 参数详情", expanded=False):
                                st.code(f"参数路径: {param_path}")
                                st.code(f"当前值: {new_value}")
                else:
                    st.info("请勾选上方的确认选项以启用编辑模式")
                    
                    # 显示参数概览（只读）
                    st.markdown("**可编辑参数预览:**")
                    for param_info in editable_params:
                        param_name = param_info["name"]
                        current_value = param_info["current_value"]
                        st.markdown(f"- **{param_name}**: `{current_value}`")
    else:
        st.info("🔍 该工作流暂无可识别的可编辑参数")
        
        # 显示所有节点概览
        with st.expander("🧪 高级: 查看所有节点", expanded=False):
            if workflow_json:
                for node_id, node_data in workflow_json.items():
                    if isinstance(node_data, dict):
                        class_type = node_data.get("class_type", "Unknown")
                        node_title = node_data.get("_meta", {}).get("title", class_type)
                        st.markdown(f"**节点 {node_id}**: {node_title} ({class_type})")
            else:
                st.warning("无法获取工作流JSON数据")
    
    # 输入输出目录设置
    st.markdown("---")
    st.subheader("📁 输入输出目录设置")
    
    workflow_type = classify_workflow_type(workflow_json)
    
    # 根据工作流类型显示不同的设置选项
    if workflow_type in ["图生图", "图片编辑", "图生视频"]:
        # 需要图片输入的工作流
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**📥 输入图片目录**")
            default_input_dir = workflow.get('metadata', {}).get('default_input_dir', '')
            
            input_dir_key = f"{workflow.get('key', 'unknown')}_input_dir"
            if input_dir_key not in st.session_state:
                st.session_state[input_dir_key] = default_input_dir
            
            current_input_dir = st.text_input(
                "输入目录路径",
                value=st.session_state[input_dir_key],
                key=f"{input_dir_key}_input",
                help="将需要处理的图片放在此目录中"
            )
            
            col1_1, col1_2 = st.columns(2)
            with col1_1:
                if st.button("📂 浏览选择", key=f"{input_dir_key}_browse"):
                    selected_dir = select_folder_with_dialog()
                    if selected_dir:
                        st.session_state[input_dir_key] = selected_dir
                        st.rerun()
            
            with col1_2:
                if st.button("📁 创建目录", key=f"{input_dir_key}_create"):
                    if current_input_dir:
                        try:
                            os.makedirs(current_input_dir, exist_ok=True)
                            st.success(f"目录已创建: {current_input_dir}")
                            st.session_state[input_dir_key] = current_input_dir
                        except Exception as e:
                            st.error(f"创建目录失败: {str(e)}")
            
            # 显示目录状态和批量处理选项
            if current_input_dir and os.path.exists(current_input_dir):
                try:
                    files = os.listdir(current_input_dir)
                    image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'))]
                    st.success(f"✅ 目录存在，包含 {len(image_files)} 个图片文件")

                    # 自动批量处理提示
                    if len(image_files) > 1:
                        st.info(f"🔄 检测到 {len(image_files)} 张图片，将自动进行批量处理")
                        st.markdown("💡 **批量处理说明**：系统将依次处理所有图片，生成的结果会自动保存到输出目录")
                    elif len(image_files) == 1:
                        st.info(f"📸 检测到 1 张图片，将进行单张处理")
                    else:
                        st.warning("⚠️ 输入目录中没有找到图片文件")

                except:
                    st.warning("⚠️ 无法读取目录内容")
            elif current_input_dir:
                st.warning("⚠️ 目录不存在，请创建或选择有效目录")
        
        with col2:
            st.markdown("**📤 输出目录**")
            default_output_dir = workflow.get('metadata', {}).get('default_output_dir', '')
            
            output_dir_key = f"{workflow.get('key', 'unknown')}_output_dir"
            if output_dir_key not in st.session_state:
                st.session_state[output_dir_key] = default_output_dir
            
            current_output_dir = st.text_input(
                "输出目录路径",
                value=st.session_state[output_dir_key],
                key=f"{output_dir_key}_input",
                help="处理后的图片/视频将保存在此目录中"
            )
            
            col2_1, col2_2 = st.columns(2)
            with col2_1:
                if st.button("📂 浏览选择", key=f"{output_dir_key}_browse"):
                    selected_dir = select_folder_with_dialog()
                    if selected_dir:
                        st.session_state[output_dir_key] = selected_dir
                        st.rerun()
            
            with col2_2:
                if st.button("📁 创建目录", key=f"{output_dir_key}_create"):
                    if current_output_dir:
                        try:
                            os.makedirs(current_output_dir, exist_ok=True)
                            st.success(f"目录已创建: {current_output_dir}")
                            st.session_state[output_dir_key] = current_output_dir
                        except Exception as e:
                            st.error(f"创建目录失败: {str(e)}")
            
            # 显示目录状态
            if current_output_dir and os.path.exists(current_output_dir):
                st.success("✅ 输出目录已就绪")
            elif current_output_dir:
                st.warning("⚠️ 目录不存在，请创建或选择有效目录")
    
    elif workflow_type in ["文生图", "文生视频"]:
        # 只需要输出目录的工作流
        st.markdown("**📤 输出目录设置**")
        
        output_type = "视频" if workflow_type == "文生视频" else "图片"
        st.info(f"此工作流会生成{output_type}，请设置输出目录")
        
        default_output_dir = workflow.get('metadata', {}).get('default_output_dir', '')
        
        output_dir_key = f"{workflow.get('key', 'unknown')}_output_dir"
        if output_dir_key not in st.session_state:
            st.session_state[output_dir_key] = default_output_dir
        
        current_output_dir = st.text_input(
            f"输出目录路径",
            value=st.session_state[output_dir_key],
            key=f"{output_dir_key}_input",
            help=f"生成的{output_type}将保存在此目录中"
        )
        
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📂 浏览选择", key=f"{output_dir_key}_browse"):
                selected_dir = select_folder_with_dialog()
                if selected_dir:
                    st.session_state[output_dir_key] = selected_dir
                    st.rerun()
        
        with col2:
            if st.button("📁 创建目录", key=f"{output_dir_key}_create"):
                if current_output_dir:
                    try:
                        os.makedirs(current_output_dir, exist_ok=True)
                        st.success(f"目录已创建: {current_output_dir}")
                        st.session_state[output_dir_key] = current_output_dir
                    except Exception as e:
                        st.error(f"创建目录失败: {str(e)}")
        
        with col3:
            if current_output_dir and os.path.exists(current_output_dir):
                if st.button("📂 打开目录", key=f"{output_dir_key}_open"):
                    try:
                        if os.name == "nt":  # Windows
                            os.startfile(current_output_dir)
                        else:  # Linux/Mac
                            import subprocess
                            subprocess.run(["xdg-open", current_output_dir])
                        st.success("已在文件管理器中打开目录")
                    except:
                        st.warning("无法打开目录")
        
        # 显示目录状态
        if current_output_dir and os.path.exists(current_output_dir):
            st.success("✅ 输出目录已就绪")
        elif current_output_dir:
            st.warning("⚠️ 目录不存在，请创建或选择有效目录")
    
    else:
        st.info("🔍 工作流类型未明确，请手动设置输入输出目录")
    
    # 运行控制
    st.markdown("---")
    st.subheader("🚀 运行控制")
    
    # 检查工作流状态
    is_dev_workflow = "DEV" in workflow.get('metadata', {}).get('tags', [])
    
    if is_dev_workflow:
        st.warning("⚠️ 这是开发中的工作流，仅供查看，无法运行")
        st.button("▶️ 开始运行", disabled=True, help="开发中的工作流无法运行")
    else:
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("▶️ 开始运行", type="primary", use_container_width=True):
                # 验证配置
                errors = []

                # 检查ComfyUI工作流目录
                workflow_dir = st.session_state.get("comfyui_workflow_dir")
                if not workflow_dir:
                    errors.append("请先设置ComfyUI工作流目录")

                # 检查输入目录（如果需要）
                if workflow_type in ["图生图", "图片编辑", "图生视频"]:
                    input_dir_key = f"{workflow.get('key', 'unknown')}_input_dir"
                    input_dir = st.session_state.get(input_dir_key, '')
                    if not input_dir or not os.path.exists(input_dir):
                        errors.append("请设置有效的输入目录")

                # 检查输出目录
                output_dir_key = f"{workflow.get('key', 'unknown')}_output_dir"
                output_dir = st.session_state.get(output_dir_key, '')
                if not output_dir:
                    errors.append("请设置输出目录")

                if errors:
                    for error in errors:
                        st.error(f"❌ {error}")
                else:
                    # 自动检测是否需要批量处理
                    input_dir = st.session_state.get(input_dir_key, '')
                    batch_mode = False

                    if input_dir and os.path.exists(input_dir):
                        try:
                            files = os.listdir(input_dir)
                            image_files = [f for f in files if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp', '.gif'))]
                            batch_mode = len(image_files) > 1
                            print(f"🔍 自动检测批量模式: {batch_mode} (发现 {len(image_files)} 张图片)")
                        except Exception as e:
                            print(f"⚠️ 检测输入目录失败: {str(e)}")

                    # 准备运行参数
                    run_params = {
                        "workflow_key": workflow.get('key', 'unknown'),
                        "parameters": {},  # 这里可以添加参数收集逻辑
                        "input_dir": st.session_state.get(input_dir_key, ''),
                        "output_dir": st.session_state.get(output_dir_key, ''),
                        "workflow_dir": workflow_dir,
                        "batch_mode": batch_mode
                    }

                    st.info("🔄 正在提交任务到运行队列...")
                    try:
                        from .workflow_utils import api_request, get_auth_headers
                        import asyncio

                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        resp = loop.run_until_complete(
                            api_request("POST", "/run", headers=get_auth_headers(), json=run_params)
                        )
                        loop.close()

                        if isinstance(resp, dict) and resp.get("task_id"):
                            task_id = resp['task_id']
                            st.success(f"✅ 任务已提交: {task_id}")

                            # 在配置页面直接显示实时状态
                            st.markdown("---")
                            st.markdown("### 📊 任务执行状态")

                            if batch_mode:
                                st.info(f"🔄 批量处理已开始，将处理输入目录中的所有图片")
                                show_inline_task_status(task_id, is_batch=True)
                            else:
                                st.info("🔄 任务已开始执行")
                                show_inline_task_status(task_id, is_batch=False)
                        else:
                            st.warning("未获取到任务信息，请检查后台日志")
                    except Exception as e:
                        st.error(f"API 请求失败: {e}")
        
        with col2:
            if st.button("💾 保存到工作流文件", use_container_width=True):
                try:
                    overrides = st.session_state.workflow_params.get(workflow.get('key', 'unknown'), {})
                    if not overrides:
                        st.info("当前没有可保存的参数变更")
                    else:
                        loop = asyncio.get_event_loop() if asyncio.get_event_loop().is_running() else None
                        # 直接调用 API 保存
                        import requests
                        headers = {}
                        if 'token' in st.session_state:
                            headers['Authorization'] = f"Bearer {st.session_state.token}"
                        resp = requests.post(
                            f"http://localhost:{os.getenv('API_PORT', '18001')}/workflows/{workflow.get('key')}/save",
                            json={"parameters": overrides},
                            headers=headers,
                            timeout=15,
                        )
                        if resp.status_code == 200:
                            data = resp.json()
                            if data.get('success'):
                                result_data = data.get('data', {})
                                file_path = result_data.get('file_path', '')
                                verifications = result_data.get('verifications', {})

                                st.success(f"✅ 已保存到文件: {file_path}")

                                # 标记参数已保存，激活重新载入按钮
                                if "workflow_saved_status" not in st.session_state:
                                    st.session_state.workflow_saved_status = {}
                                st.session_state.workflow_saved_status[workflow_key] = True

                                # 显示验证结果
                                if verifications:
                                    failed_params = [k for k, v in verifications.items() if not v.get('ok', True)]
                                    if failed_params:
                                        st.warning(f"⚠️ 以下参数可能未正确保存: {', '.join(failed_params)}")
                                    else:
                                        st.info(f"✅ 所有 {len(verifications)} 个参数已验证保存成功")
                            else:
                                st.error(f"❌ 保存失败: {data.get('message', '未知错误')}")
                        else:
                            st.error(f"❌ 保存失败 HTTP {resp.status_code}: {resp.text}")
                except Exception as e:
                    st.error(f"❌ 保存失败: {str(e)}")
        
        with col3:
            # 检查是否有参数变更（无论是否已保存）
            workflow_key = workflow.get('key', 'unknown')
            current_overrides = st.session_state.get("workflow_params", {}).get(workflow_key, {})
            saved_status = st.session_state.get("workflow_saved_status", {}).get(workflow_key, False)

            # 按钮状态：如果有未保存的参数变更则显示警告色，否则正常
            button_type = "secondary" if current_overrides and not saved_status else "primary"

            if st.button("🔄 重新载入工作流", use_container_width=True, type=button_type):
                # 立即设置重新载入时间戳，确保组件key会改变
                import time
                st.session_state[f"workflow_reload_timestamp_{workflow_key}"] = time.time()

                try:
                    # 重新获取工作流数据以载入最新参数，使用force_reload强制从磁盘读取
                    resp = api_request("GET", f"/workflows/{workflow_key}?force_reload=true", headers=get_auth_headers())
                    if isinstance(resp, dict) and resp.get("success"):
                        # 获取最新的工作流数据
                        updated_workflow_data = resp.get("data")

                        # 强制清除所有相关的缓存数据
                        keys_to_clear = []
                        for key in st.session_state.keys():
                            if workflow_key in key:
                                keys_to_clear.append(key)

                        for key in keys_to_clear:
                            del st.session_state[key]

                        # 特别清除工作流相关的缓存
                        cache_keys = [
                            "workflow_params",
                            "workflow_saved_status",
                            "workflows_cache",
                            "current_workflow",
                            "selected_workflow",
                            f"workflow_data_{workflow_key}",
                            f"workflow_info_{workflow_key}"
                        ]

                        for cache_key in cache_keys:
                            if cache_key in st.session_state:
                                if isinstance(st.session_state[cache_key], dict) and workflow_key in st.session_state[cache_key]:
                                    del st.session_state[cache_key][workflow_key]
                                elif cache_key.endswith(workflow_key):
                                    del st.session_state[cache_key]
                                else:
                                    del st.session_state[cache_key]

                        # 更新当前工作流数据为最新数据
                        if updated_workflow_data:
                            # 如果当前页面有工作流数据，更新它
                            if hasattr(st.session_state, 'current_workflow_data'):
                                st.session_state.current_workflow_data = updated_workflow_data

                        # 重置目录配置到默认值
                        input_dir_key = f"{workflow_key}_input_dir"
                        output_dir_key = f"{workflow_key}_output_dir"

                        if updated_workflow_data and input_dir_key in st.session_state:
                            st.session_state[input_dir_key] = updated_workflow_data.get('metadata', {}).get('default_input_dir', '')

                        if updated_workflow_data and output_dir_key in st.session_state:
                            st.session_state[output_dir_key] = updated_workflow_data.get('metadata', {}).get('default_output_dir', '')

                        # 强制重新设置ComfyUI目录以触发重新读取
                        if "comfyui_workflow_dir" in st.session_state:
                            current_dir = st.session_state.comfyui_workflow_dir
                            st.session_state.comfyui_workflow_dir = current_dir

                        # 添加重新载入时间戳，强制页面重新渲染
                        import time
                        st.session_state[f"workflow_reload_timestamp_{workflow_key}"] = time.time()

                        # 如果有未保存的参数，给出警告
                        if current_overrides and not saved_status:
                            st.warning("⚠️ 已重新载入工作流，之前未保存的参数修改已丢失")
                        else:
                            st.success("✅ 已重新载入最新的工作流参数")
                        st.rerun()
                    else:
                        st.error("重新载入失败，请检查后端连接")
                        # 显示详细的响应信息用于调试
                        if isinstance(resp, dict):
                            st.error(f"API响应: {resp}")
                        else:
                            st.error(f"响应类型: {type(resp)}, 内容: {resp}")
                except Exception as e:
                    st.error(f"重新载入失败: {e}")
                    import traceback
                    st.error(f"详细错误: {traceback.format_exc()}")
    
    # 显示当前配置概览
    with st.expander("📊 当前配置概览", expanded=False):
        workflow_key = workflow.get('key', 'unknown')
        
        st.markdown("**工作流信息:**")
        st.markdown(f"- 名称: {workflow.get('metadata', {}).get('name', '未知')}")
        st.markdown(f"- 类型: {workflow_type}")
        st.markdown(f"- 标签: {', '.join(workflow.get('metadata', {}).get('tags', []))}")
        
        st.markdown("**目录设置:**")
        input_dir = st.session_state.get(f"{workflow_key}_input_dir", '未设置')
        output_dir = st.session_state.get(f"{workflow_key}_output_dir", '未设置')
        st.markdown(f"- 输入目录: `{input_dir}`")
        st.markdown(f"- 输出目录: `{output_dir}`")
        
        st.markdown("**参数设置:**")
        params = st.session_state.workflow_params.get(workflow_key, {})
        if params:
            for param_path, value in params.items():
                st.markdown(f"- {param_path}: `{value}`")
        else:
            st.markdown("- 暂无自定义参数")


def show_inline_task_status(task_id: str, is_batch: bool = False):
    """在配置页面内联显示任务状态"""
    import time
    import asyncio
    from .workflow_utils import api_request, get_auth_headers

    # 创建状态显示容器
    status_container = st.empty()
    progress_container = st.empty()

    # 控制按钮
    col1, col2 = st.columns([1, 3])
    with col1:
        if st.button("⏹️ 停止监控", key=f"stop_inline_monitor_{task_id}"):
            st.session_state[f"stop_inline_monitor_{task_id}"] = True
            return

    with col2:
        if st.button("🔄 刷新状态", key=f"refresh_status_{task_id}"):
            st.rerun()

    # 初始化停止标志
    if f"stop_inline_monitor_{task_id}" not in st.session_state:
        st.session_state[f"stop_inline_monitor_{task_id}"] = False

    # 获取任务状态（单次检查，避免阻塞页面）
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        task_status = loop.run_until_complete(
            api_request("GET", f"/status/{task_id}", headers=get_auth_headers())
        )
        loop.close()

        if task_status and isinstance(task_status, dict):
            status = task_status.get("status", "unknown")

            # 状态显示
            status_icons = {
                "pending": "🟡 等待中",
                "running": "🔵 运行中",
                "completed": "🟢 已完成",
                "failed": "🔴 失败"
            }

            status_text = status_icons.get(status, f"⚪ {status}")
            status_container.markdown(f"**当前状态**: {status_text}")

            # 显示进度信息
            if status == "running":
                batch_progress = task_status.get("batch_progress")
                workflow_progress = task_status.get("workflow_progress")

                with progress_container.container():
                    if is_batch and batch_progress:
                        # 批量处理进度
                        current = batch_progress.get("current_index", 0)
                        total = batch_progress.get("total_count", 0)
                        current_file = batch_progress.get("current_filename", "")

                        if total > 0:
                            progress_percent = (current / total) * 100
                            st.progress(progress_percent / 100.0)
                            st.caption(f"正在处理: {current_file} ({current}/{total})")

                    elif workflow_progress:
                        # 单任务工作流进度
                        overall_progress = workflow_progress.get("overall_progress", 0.0)
                        current_node = workflow_progress.get("current_node")

                        st.progress(overall_progress / 100.0)
                        if current_node:
                            st.caption(f"当前节点: {current_node}")

                    else:
                        st.info("正在获取进度信息...")

            elif status == "completed":
                st.success("🎉 任务执行完成！")

                # 显示结果摘要
                result = task_status.get("result", {})
                if result.get("batch_summary"):
                    summary = result["batch_summary"]
                    st.info(f"📊 处理结果: 成功 {summary.get('successful', 0)} 张，失败 {summary.get('failed', 0)} 张")

            elif status == "failed":
                error_msg = task_status.get("error_message", "未知错误")
                st.error(f"❌ 任务执行失败: {error_msg}")

        else:
            status_container.warning("⚠️ 无法获取任务状态")

    except Exception as e:
        status_container.error(f"❌ 获取任务状态失败: {str(e)}")


def show_batch_progress_monitor(task_id: str):
    """显示批量处理进度监控"""
    import time

    progress_container = st.empty()
    status_container = st.empty()

    # 创建停止按钮
    stop_col1, stop_col2 = st.columns([1, 4])
    with stop_col1:
        if st.button("⏹️ 停止监控", key=f"stop_monitor_{task_id}"):
            st.session_state[f"stop_monitor_{task_id}"] = True
            return

    # 初始化停止标志
    if f"stop_monitor_{task_id}" not in st.session_state:
        st.session_state[f"stop_monitor_{task_id}"] = False

    # 监控循环
    max_checks = 120  # 最多检查10分钟
    check_count = 0

    while check_count < max_checks and not st.session_state.get(f"stop_monitor_{task_id}", False):
        try:
            # 获取任务状态
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            task_status = loop.run_until_complete(
                api_request("GET", f"/status/{task_id}", headers=get_auth_headers())
            )
            loop.close()

            if task_status and isinstance(task_status, dict):
                status = task_status.get("status", "unknown")
                batch_progress = task_status.get("batch_progress")

                # 显示整体状态
                status_container.info(f"📋 任务状态: {status}")

                # 显示批量进度
                if batch_progress:
                    current = batch_progress.get("current_index", 0)
                    total = batch_progress.get("total_count", 0)
                    current_file = batch_progress.get("current_filename", "")
                    completed = len(batch_progress.get("completed_files", []))
                    failed = len(batch_progress.get("failed_files", []))

                    # 计算进度百分比
                    progress_percent = (completed / total * 100) if total > 0 else 0

                    with progress_container.container():
                        st.markdown(f"**🔄 批量处理进度: {current}/{total}**")
                        st.progress(progress_percent / 100)

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("当前处理", current_file if current_file else "准备中")
                        with col2:
                            st.metric("已完成", completed, delta=f"+{completed}")
                        with col3:
                            st.metric("失败", failed, delta=f"+{failed}" if failed > 0 else None)

                # 检查是否完成
                if status in ["completed", "failed"]:
                    if status == "completed":
                        st.success("🎉 批量处理完成！")
                    else:
                        error_msg = task_status.get("error_message", "未知错误")
                        st.error(f"❌ 批量处理失败: {error_msg}")
                    break

            # 等待5秒后再次检查
            time.sleep(5)
            check_count += 1

        except Exception as e:
            status_container.error(f"❌ 获取进度失败: {str(e)}")
            break

    # 清理停止标志
    if f"stop_monitor_{task_id}" in st.session_state:
        del st.session_state[f"stop_monitor_{task_id}"]
