#!/usr/bin/env python3
"""
批量处理和进度监控功能测试脚本
"""

import asyncio
import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.batch_service import batch_service
from app.api.comfy_client import ComfyUIClient
from app.api.schemas import TaskInfo, TaskStatus, BatchProgress


async def test_batch_processing():
    """测试批量处理功能"""
    print("🧪 开始测试批量处理功能...")
    
    # 创建测试目录和文件
    with tempfile.TemporaryDirectory() as temp_dir:
        input_dir = os.path.join(temp_dir, "input")
        output_dir = os.path.join(temp_dir, "output")
        
        os.makedirs(input_dir)
        os.makedirs(output_dir)
        
        # 创建测试图片文件（空文件用于测试）
        test_images = ["test1.png", "test2.jpg", "test3.jpeg"]
        for img_name in test_images:
            test_file = os.path.join(input_dir, img_name)
            with open(test_file, 'wb') as f:
                f.write(b"fake_image_data")  # 写入假的图片数据
        
        print(f"📁 创建测试目录: {input_dir}")
        print(f"📁 创建输出目录: {output_dir}")
        print(f"🖼️ 创建测试图片: {test_images}")
        
        # 测试获取图片文件功能
        try:
            image_files = batch_service.get_image_files(input_dir)
            print(f"✅ 成功获取图片文件: {len(image_files)} 个")
            for img_file in image_files:
                print(f"   - {os.path.basename(img_file)}")
        except Exception as e:
            print(f"❌ 获取图片文件失败: {str(e)}")
            return False
        
        # 测试批量处理逻辑（模拟）
        try:
            # 创建模拟任务
            task = TaskInfo(
                task_id="test-task-123",
                workflow_key="test-workflow",
                status=TaskStatus.PENDING,
                created_at=asyncio.get_event_loop().time()
            )
            
            # 模拟工作流JSON
            mock_workflow = {
                "1": {
                    "class_type": "LoadImage",
                    "inputs": {
                        "image": "test.png"
                    }
                },
                "2": {
                    "class_type": "SaveImage",
                    "inputs": {
                        "images": ["1", 0]
                    }
                }
            }
            
            print("🔄 开始模拟批量处理...")
            
            # 初始化批量进度
            task.batch_progress = BatchProgress(
                current_index=0,
                total_count=len(image_files),
                completed_files=[],
                failed_files=[]
            )
            
            # 模拟处理每张图片
            for index, image_file in enumerate(image_files):
                task.batch_progress.current_index = index + 1
                task.batch_progress.current_filename = os.path.basename(image_file)
                
                print(f"📸 模拟处理第 {index + 1}/{len(image_files)} 张图片: {task.batch_progress.current_filename}")
                
                # 模拟处理时间
                await asyncio.sleep(1)
                
                # 模拟成功处理
                task.batch_progress.completed_files.append(task.batch_progress.current_filename)
                
                # 创建模拟输出文件
                output_filename = f"{os.path.splitext(task.batch_progress.current_filename)[0]}_output.png"
                output_path = os.path.join(output_dir, output_filename)
                with open(output_path, 'wb') as f:
                    f.write(b"fake_output_image_data")
                
                print(f"💾 模拟输出文件已创建: {output_filename}")
            
            print("✅ 批量处理模拟完成！")
            print(f"📊 处理结果: 成功 {len(task.batch_progress.completed_files)} 张，失败 {len(task.batch_progress.failed_files)} 张")
            
            return True
            
        except Exception as e:
            print(f"❌ 批量处理测试失败: {str(e)}")
            return False


async def test_comfyui_connection():
    """测试ComfyUI连接"""
    print("🧪 测试ComfyUI连接...")
    
    try:
        client = ComfyUIClient()
        print(f"🔗 ComfyUI地址: {client.base_url}")
        print(f"🔗 WebSocket地址: {client.ws_url}")
        
        # 测试HTTP连接
        is_connected = await client.check_connection()
        if is_connected:
            print("✅ ComfyUI HTTP连接正常")
        else:
            print("❌ ComfyUI HTTP连接失败")
            return False
        
        # 测试获取队列状态
        queue_status = await client.get_queue_status()
        print(f"📋 队列状态: {queue_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ ComfyUI连接测试失败: {str(e)}")
        return False


def test_progress_data_structures():
    """测试进度数据结构"""
    print("🧪 测试进度数据结构...")
    
    try:
        from app.api.schemas import BatchProgress, WorkflowProgress, NodeProgress
        
        # 测试BatchProgress
        batch_progress = BatchProgress(
            current_index=2,
            total_count=5,
            current_filename="test2.jpg",
            completed_files=["test1.png"],
            failed_files=[]
        )
        print(f"✅ BatchProgress创建成功: {batch_progress.model_dump()}")
        
        # 测试NodeProgress
        node_progress = NodeProgress(
            node_id="1",
            node_title="LoadImage",
            status="executing",
            progress_percent=50.0
        )
        print(f"✅ NodeProgress创建成功: {node_progress.model_dump()}")
        
        # 测试WorkflowProgress
        workflow_progress = WorkflowProgress(
            prompt_id="test-prompt-123",
            status="running",
            nodes=[node_progress],
            current_node="1",
            overall_progress=25.0
        )
        print(f"✅ WorkflowProgress创建成功: {workflow_progress.model_dump()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 进度数据结构测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始功能测试...")
    print("=" * 50)
    
    # 测试1: 进度数据结构
    test1_result = test_progress_data_structures()
    print("=" * 50)
    
    # 测试2: ComfyUI连接
    test2_result = await test_comfyui_connection()
    print("=" * 50)
    
    # 测试3: 批量处理逻辑
    test3_result = await test_batch_processing()
    print("=" * 50)
    
    # 总结测试结果
    print("📊 测试结果总结:")
    print(f"   进度数据结构: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   ComfyUI连接: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"   批量处理逻辑: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    all_passed = test1_result and test2_result and test3_result
    print(f"\n🎯 总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if not all_passed:
        print("\n💡 提示:")
        if not test2_result:
            print("   - 请确保ComfyUI服务正在运行")
            print("   - 检查COMFYUI_BASE_URL配置是否正确")
        print("   - 查看上述错误信息进行调试")


if __name__ == "__main__":
    asyncio.run(main())
